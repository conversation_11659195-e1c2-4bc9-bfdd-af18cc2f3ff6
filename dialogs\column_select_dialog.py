"""Dialog for selecting columns and depth ranges."""

from __future__ import annotations

from typing import Any, Dict, List, Optional

import tkinter as tk
from tkinter import ttk, messagebox

import config
import processing
from .depth_dialog import get_depth_ranges


class ColumnSelectionDialog(tk.Toplevel):
    """Dialog to choose X/Y/Class/Z columns and depth ranges."""

    def __init__(self, parent: tk.Misc, las_files: List, preloaded_excel_df=None) -> None:
        super().__init__(parent)
        self.las_files = las_files
        self.preloaded_excel_df = preloaded_excel_df
        self.result: Optional[Dict[str, Any]] = None
        self.depth_ranges: Dict[str, Any] = {}
        self.title("Select Columns")
        self.geometry("400x300")
        self._build_ui()

    def _build_ui(self) -> None:
        columns = set(self.las_files[0].curves.keys())
        for las in self.las_files[1:]:
            columns.intersection_update(las.curves.keys())
        cols = sorted(columns)
        self.x_var = tk.StringVar(value=cols[0] if cols else "")
        self.y_var = tk.StringVar(value=cols[1] if len(cols) > 1 else "")
        self.class_var = tk.StringVar(value="")
        self.z_var = tk.StringVar(value="")

        ttk.Label(self, text="X Column:").pack(anchor="w", padx=5, pady=2)
        ttk.Combobox(self, textvariable=self.x_var, values=cols).pack(fill=tk.X, padx=5)
        ttk.Label(self, text="Y Column:").pack(anchor="w", padx=5, pady=2)
        ttk.Combobox(self, textvariable=self.y_var, values=cols).pack(fill=tk.X, padx=5)
        ttk.Label(self, text="Class Column (optional):").pack(anchor="w", padx=5, pady=2)
        ttk.Combobox(self, textvariable=self.class_var, values=[""] + cols).pack(fill=tk.X, padx=5)
        ttk.Label(self, text="Z Column (optional):").pack(anchor="w", padx=5, pady=2)
        ttk.Combobox(self, textvariable=self.z_var, values=[""] + cols).pack(fill=tk.X, padx=5)

        # Depth ranges section
        depth_frame = ttk.Frame(self)
        depth_frame.pack(fill=tk.X, padx=5, pady=5)
        ttk.Button(depth_frame, text="Select Depth Ranges", command=self._select_ranges).pack(side=tk.LEFT)

        # Status label for depth ranges (will be updated when ranges are selected)
        initial_status = ""
        if self.preloaded_excel_df is not None:
            well_count = self.preloaded_excel_df['Well'].nunique()
            initial_status = f"📊 Excel data available for {well_count} wells"
        else:
            initial_status = "⚠ No depth ranges selected yet"

        self._depth_status_label = ttk.Label(depth_frame, text=initial_status, foreground="gray")
        self._depth_status_label.pack(side=tk.LEFT, padx=(10, 0))

        # Button frame
        button_frame = ttk.Frame(self)
        button_frame.pack(fill=tk.X, padx=5, pady=10)
        ttk.Button(button_frame, text="Submit", command=self._on_submit).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Cancel", command=self._on_cancel).pack(side=tk.RIGHT, padx=5)

    def _select_ranges(self) -> None:
        """Select depth ranges and ensure proper return to column selection dialog."""
        try:
            # Release any grab this dialog might have
            try:
                self.grab_release()
            except tk.TclError:
                pass  # No grab to release

            # Temporarily hide this dialog while depth selection is active
            self.withdraw()

            # Get depth ranges
            depth_ranges = get_depth_ranges(self.las_files, config.log_keywords, self.preloaded_excel_df)

            # Restore this dialog
            self.deiconify()
            self.lift()  # Bring to front
            self.focus_set()  # Give focus back to this dialog

            # Re-establish grab for this dialog
            try:
                self.grab_set()
            except tk.TclError:
                pass  # Grab might not be available

            # Store the result
            if depth_ranges:
                self.depth_ranges = depth_ranges
                # Update UI to show that depth ranges have been selected
                if hasattr(self, '_depth_status_label'):
                    well_count = len(depth_ranges)
                    self._depth_status_label.config(text=f"✓ Depth ranges selected for {well_count} wells")
                else:
                    # Create status label if it doesn't exist
                    self._depth_status_label = ttk.Label(self, text=f"✓ Depth ranges selected for {len(depth_ranges)} wells")
                    self._depth_status_label.pack(pady=2)
            else:
                # User cancelled depth range selection
                if hasattr(self, '_depth_status_label'):
                    self._depth_status_label.config(text="⚠ No depth ranges selected")

        except Exception as e:
            # Ensure dialog is restored even if there's an error
            self.deiconify()
            self.lift()
            self.focus_set()
            try:
                self.grab_set()
            except tk.TclError:
                pass
            messagebox.showerror("Error", f"Error during depth range selection: {str(e)}")
            raise

    def _on_submit(self) -> None:
        if not self.x_var.get() or not self.y_var.get():
            messagebox.showerror("Error", "X and Y columns are required")
            return
        self.result = {
            "x_col": self.x_var.get(),
            "y_col": self.y_var.get(),
            "class_col": self.class_var.get() or None,
            "z_col": self.z_var.get() or None,
            "depth_ranges": self.depth_ranges or get_depth_ranges(self.las_files, config.log_keywords, self.preloaded_excel_df),
        }
        val = processing.validate_data_for_plotting(
            self.las_files,
            self.result["x_col"],
            self.result["y_col"],
            self.result["class_col"],
            self.result["depth_ranges"],
            self.result["z_col"],
        )
        self.result["validation_stats"] = val["stats"]
        self.destroy()

    def _on_cancel(self) -> None:
        self.result = None
        self.destroy()

    def show(self) -> Optional[Dict[str, Any]]:
        self.wait_window()
        return self.result
